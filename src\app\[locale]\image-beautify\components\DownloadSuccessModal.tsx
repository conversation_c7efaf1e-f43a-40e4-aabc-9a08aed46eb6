/**
 * 下载成功弹窗组件
 */

'use client'

import { useCallback } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { useTranslation } from '@/lib/i18n/client'
import Button from '@/components/ui/Button'
import Icon from '@/components/ui/Icon'

interface DownloadSuccessModalProps {
  isOpen: boolean
  onClose: () => void
  onDownload: () => void
  isDownloading?: boolean
}

export default function DownloadSuccessModal({
  isOpen,
  onClose,
  onDownload,
  isDownloading = false
}: DownloadSuccessModalProps) {
  const { t } = useTranslation()

  const handleDownload = useCallback(() => {
    onDownload()
  }, [onDownload])

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="relative w-full max-w-sm bg-white rounded-2xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 内容区域 */}
            <div className="p-8 text-center">
              {/* 成功图标 */}
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto bg-amber-100 rounded-full flex items-center justify-center">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center">
                    <Icon name="wancheng" size="sm" className="text-white" />
                  </div>
                </div>
              </div>

              {/* 标题和描述 */}
              <div className="mb-8">
                <h2 className="text-xl font-bold text-gray-900 mb-2">
                  已提交
                </h2>
                <p className="text-sm text-gray-600">
                  信息提交成功，免费下载图片
                </p>
              </div>

              {/* 下载按钮 */}
              <Button
                variant="primary"
                onClick={handleDownload}
                loading={isDownloading}
                className="w-full bg-gray-900 hover:bg-gray-800 text-white py-3"
              >
                下载图片
              </Button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
