/**
 * 文件处理工具函数
 */

import type { SupportedImageFormat, ProcessingType } from '@/lib/constants/image'
import { isMiniProgramEnvironment, saveImageInMiniProgram } from './miniprogram'

// 图片文件接口
export interface ImageFile {
  file: File
  url: string
  width: number
  height: number
  size: number
  format: SupportedImageFormat
}

// 图片尺寸接口
export interface ImageDimensions {
  width: number
  height: number
}

/**
 * 获取图片尺寸
 */
export function getImageDimensions(url: string): Promise<ImageDimensions> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    
    img.src = url
  })
}

/**
 * 创建图片文件对象
 */
export async function createImageFile(file: File): Promise<ImageFile> {
  const url = URL.createObjectURL(file)

  try {
    const dimensions = await getImageDimensions(url)

    return {
      file,
      url,
      width: dimensions.width,
      height: dimensions.height,
      size: file.size,
      format: file.type as SupportedImageFormat
    }
  } catch (error) {
    // 如果获取尺寸失败，清理 URL 并抛出错误
    URL.revokeObjectURL(url)
    throw error
  }
}

/**
 * 清理对象 URL
 */
export function cleanupObjectURL(url: string): void {
  if (url && url.startsWith('blob:')) {
    URL.revokeObjectURL(url)
  }
}

/**
 * 生成处理后的文件名
 */
export function generateFileName(originalName: string, processingType: ProcessingType): string {
  const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
  const extension = originalName.split('.').pop() || 'jpg'
  
  const typeMap = {
    'teeth-whitening': 'teeth-whitened',
    'beauty-enhancement': 'beautified'
  }
  
  const suffix = typeMap[processingType] || 'processed'
  const timestamp = Date.now()
  
  return `${nameWithoutExt}_${suffix}_${timestamp}.${extension}`
}

/**
 * 下载文件
 */
export async function downloadFile(
  file: File,
  filename?: string,
  translations?: { saveImageTip: string; close: string }
): Promise<void> {
  // 检测是否在小程序环境
  if (isMiniProgramEnvironment()) {
    try {
      await saveImageInMiniProgram(file, translations)
      return
    } catch (error) {
      console.warn('Failed to save image in miniprogram environment, fallback to normal download:', error)
      // 如果小程序环境保存失败，降级到普通下载方式
    }
  }

  // 普通浏览器环境的下载方式
  const url = URL.createObjectURL(file)
  const link = document.createElement('a')

  link.href = url
  link.download = filename || file.name
  link.style.display = 'none'

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 清理 URL
  URL.revokeObjectURL(url)
}

/**
 * 压缩图片
 */
export function compressImage(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }

      // 设置画布尺寸
      canvas.width = width
      canvas.height = height

      // 绘制图片
      ctx.drawImage(img, 0, 0, width, height)

      // 转换为 Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to compress image'))
          }
        },
        file.type,
        quality
      )
    }

    img.onerror = () => {
      reject(new Error('Failed to load image for compression'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * 创建缩略图
 */
export function createThumbnail(
  file: File,
  maxSize: number = 200
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      const { width, height } = img
      const ratio = Math.min(maxSize / width, maxSize / height)
      const newWidth = width * ratio
      const newHeight = height * ratio

      canvas.width = newWidth
      canvas.height = newHeight

      ctx.drawImage(img, 0, 0, newWidth, newHeight)
      resolve(canvas.toDataURL())
    }

    img.onerror = () => {
      reject(new Error('Failed to create thumbnail'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * 将 Blob 转换为 File
 */
export function blobToFile(blob: Blob, filename: string, type?: string): File {
  return new File([blob], filename, {
    type: type || blob.type,
    lastModified: Date.now()
  })
}

/**
 * 读取文件为 ArrayBuffer
 */
export function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      if (reader.result instanceof ArrayBuffer) {
        resolve(reader.result)
      } else {
        reject(new Error('Failed to read file as ArrayBuffer'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    
    reader.readAsArrayBuffer(file)
  })
}

/**
 * 读取文件为 Data URL
 */
export function readFileAsDataURL(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else {
        reject(new Error('Failed to read file as Data URL'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    
    reader.readAsDataURL(file)
  })
}
