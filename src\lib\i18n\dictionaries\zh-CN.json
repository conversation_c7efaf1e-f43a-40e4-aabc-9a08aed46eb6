{"common": {"loading": "加载中...", "error": "出错了", "retry": "重试", "cancel": "取消", "confirm": "确认", "save": "保存", "edit": "编辑", "delete": "删除", "back": "返回", "next": "下一步", "previous": "上一步", "close": "关闭"}, "navigation": {"home": "首页", "about": "关于我们", "products": "产品服务", "solutions": "解决方案", "imageBeautify": "图片美化", "news": "新闻动态", "contact": "联系我们", "language": "语言", "getStarted": "开始体验"}, "hero": {"title": {"line1": "创新驱动", "line2": "数字化未来"}, "subtitle": "Lumii 致力于为企业提供前沿的数字化解决方案，通过创新技术推动业务增长，引领行业数字化转型的新时代。", "cta": {"primary": "立即开始", "secondary": "了解更多"}, "stats": {"clients": "企业客户", "uptime": "服务可用性", "support": "技术支持"}}, "about": {"title": "关于我们", "subtitle": "致力于创新，推动数字化未来", "hero": {"title": "塑造数字化未来", "description": "Lumii 是一家专注于创新技术解决方案的公司，我们致力于帮助企业实现数字化转型，提升业务效率，创造更大价值。"}, "mission": {"title": "我们的使命", "description": "通过前沿技术和创新思维，为客户提供卓越的数字化解决方案，推动行业进步，创造可持续的商业价值。"}, "values": {"title": "核心价值观", "innovation": {"title": "创新驱动", "description": "持续探索新技术，推动产品和服务的创新发展"}, "excellence": {"title": "追求卓越", "description": "以最高标准要求自己，为客户提供优质的产品和服务"}, "collaboration": {"title": "协作共赢", "description": "与客户、合作伙伴携手合作，共同创造价值"}}, "team": {"title": "我们的团队", "description": "由经验丰富的技术专家和行业领袖组成，拥有深厚的技术积累和丰富的项目经验。"}}, "footer": {"company": "公司信息", "products": "产品服务", "support": "客户支持", "legal": "法律信息", "copyright": "版权所有", "allRightsReserved": "保留所有权利", "links": {"coreProducts": "核心产品", "enterprise": "企业版", "developerTools": "开发者工具", "apiDocs": "API 文档", "solutions": "解决方案", "enterpriseDigital": "企业数字化", "smartManufacturing": "智能制造", "fintech": "金融科技", "edtech": "教育科技", "helpCenter": "帮助中心", "documentation": "技术文档", "community": "社区论坛", "contactSupport": "联系支持", "careers": "招聘信息", "investors": "投资者关系", "privacyPolicy": "隐私政策", "termsOfService": "服务条款", "cookiePolicy": "<PERSON><PERSON> 政策"}, "description": "致力于为企业提供创新的数字化解决方案，推动业务增长和数字化转型。"}, "metadata": {"title": "Lumii - 创新驱动数字化未来", "description": "Lumii 致力于为企业提供前沿的数字化解决方案，通过创新技术推动业务增长，引领行业数字化转型的新时代。", "keywords": "数字化转型, 企业解决方案, 创新技术, 业务增长"}, "imageEditor": {"title": "图片美化", "subtitle": "AI 智能图片处理", "upload": {"title": "上传图片", "description": "选择要处理的图片文件", "dragText": "拖拽图片到此处，或点击选择文件", "instruction": "请上传正面足光照片；保持微笑，露出牙齿", "supportedFormats": "支持 JPG、PNG、WebP 格式", "maxSize": "最大文件大小：10MB", "selectFile": "上传照片", "uploading": "上传中...", "uploadSuccess": "上传成功", "uploadError": "上传失败"}, "processing": {"teethWhitening": "牙齿美白", "teethWhiteningApplied": "已美白", "beautyEnhancement": "一键美颜", "beautyEnhancementApplied": "已美颜", "processing": "处理中...", "completed": "处理完成", "failed": "处理失败"}, "canvas": {"dragToMove": "拖拽移动图片", "scrollToZoom": "滚轮缩放图片", "doubleClickReset": "双击重置位置"}, "download": {"title": "下载图片", "downloading": "下载中...", "success": "图片下载成功", "error": "下载失败"}, "compare": {"enter": "进入对比模式", "exit": "退出对比模式"}, "errors": {"unsupportedFormat": "不支持的文件格式", "fileTooLarge": "文件大小超出限制", "uploadFailed": "文件上传失败", "processingFailed": "图片处理失败", "downloadFailed": "图片下载失败", "canvasError": "画布初始化失败", "networkError": "网络连接错误", "serverError": "服务器错误"}, "actions": {"save": "保存", "reupload": "重新上传", "retry": "重试", "reset": "重置", "cancel": "取消"}, "miniprogram": {"saveImageTip": "长按图片保存到相册", "close": "关闭"}}, "retention": {"title": "免费AI变美", "subTitle": "提交后即可下载", "userName": "姓名", "userNamePlaceholder": "输入姓名", "city": "城市", "cityPlaceholder": "输入城市", "phone": "手机号码", "phonePlaceholder": "输入手机号码", "verificationCode": "验证码", "verificationCodePlaceholder": "输入验证码", "sendCode": "获取验证码", "submit": "提交", "cancel": "取消"}}