/**
 * 用户信息收集弹窗组件
 */

'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { useTranslation } from '@/lib/i18n/client'
import Button from '@/components/ui/Button'
import Icon from '@/components/ui/Icon'
import retentionBg from '/public/images/retentionBg.png'


interface UserInfo {
  name: string
  phone: string
  verificationCode: string
}

interface UserInfoModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (userInfo: UserInfo) => void
  isSubmitting?: boolean
}

export default function UserInfoModal({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false
}: UserInfoModalProps) {
  const { t } = useTranslation()
  const [userInfo, setUserInfo] = useState<UserInfo>({
    name: '',
    phone: '',
    verificationCode: ''
  })
  const [isCodeSent, setIsCodeSent] = useState(false)
  const [countdown, setCountdown] = useState(0)

  // 发送验证码
  const handleSendCode = useCallback(async () => {
    if (!userInfo.phone.trim()) {
      return
    }
    
    // TODO: 实际发送验证码的逻辑
    setIsCodeSent(true)
    setCountdown(60)
    
    // 倒计时
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          setIsCodeSent(false)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }, [userInfo.phone])

  // 处理表单提交
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    if (userInfo.name.trim() && userInfo.phone.trim() && userInfo.verificationCode.trim()) {
      onSubmit(userInfo)
    }
  }, [userInfo, onSubmit])

  // 处理输入变化
  const handleInputChange = useCallback((field: keyof UserInfo, value: string) => {
    setUserInfo(prev => ({ ...prev, [field]: value }))
  }, [])

  // 重置表单
  const handleClose = useCallback(() => {
    setUserInfo({ name: '', phone: '', verificationCode: '' })
    setIsCodeSent(false)
    setCountdown(0)
    onClose()
  }, [onClose])

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4"
          onClick={handleClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="relative w-full max-w-md bg-gradient-to-br from-amber-100 to-orange-200 rounded-2xl overflow-hidden shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 背景装饰图片区域 */}
            <div className="relative h-48 bg-gradient-to-br from-amber-200 to-orange-300 overflow-hidden">
              {/* 这里可以放置装饰性的图片或图案 */}
              <div className="absolute inset-0 bg-gradient-to-br from-amber-400/20 to-orange-500/20" />
              
              {/* 关闭按钮 */}
              <button
                onClick={handleClose}
                className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center bg-white/20 hover:bg-white/30 rounded-full transition-colors"
                disabled={isSubmitting}
              >
                <Icon name="guanbi" size="sm" className="text-white" />
              </button>
            </div>

            {/* 表单内容区域 */}
            <div className="p-6 bg-gradient-to-br from-amber-50 to-orange-100">
              {/* 标题 */}
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-amber-900 mb-2">
                  免费AI变美
                </h2>
                <p className="text-sm text-amber-700">
                  请填写信息可下载
                </p>
              </div>

              {/* 表单 */}
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* 姓名输入 */}
                <div className="flex space-x-4">
                  <div className="flex-1">
                    <label className="block text-sm text-amber-800 mb-1">
                      姓名
                    </label>
                    <input
                      type="text"
                      value={userInfo.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="请输入姓名"
                      className="w-full px-3 py-2 bg-white/70 border border-amber-200 rounded-lg text-amber-900 placeholder-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent"
                      disabled={isSubmitting}
                      required
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm text-amber-800 mb-1">
                      城市
                    </label>
                    <input
                      type="text"
                      placeholder="请输入城市"
                      className="w-full px-3 py-2 bg-white/70 border border-amber-200 rounded-lg text-amber-900 placeholder-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent"
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                {/* 手机号输入 */}
                <div>
                  <label className="block text-sm text-amber-800 mb-1">
                    手机号码
                  </label>
                  <input
                    type="tel"
                    value={userInfo.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="请输入手机号码"
                    className="w-full px-3 py-2 bg-white/70 border border-amber-200 rounded-lg text-amber-900 placeholder-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent"
                    disabled={isSubmitting}
                    required
                  />
                </div>

                {/* 验证码输入 */}
                <div>
                  <label className="block text-sm text-amber-800 mb-1">
                    验证码
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={userInfo.verificationCode}
                      onChange={(e) => handleInputChange('verificationCode', e.target.value)}
                      placeholder="请输入验证码"
                      className="flex-1 px-3 py-2 bg-white/70 border border-amber-200 rounded-lg text-amber-900 placeholder-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-400 focus:border-transparent"
                      disabled={isSubmitting}
                      required
                    />
                    <button
                      type="button"
                      onClick={handleSendCode}
                      disabled={isSubmitting || !userInfo.phone.trim() || isCodeSent}
                      className="px-4 py-2 bg-amber-600 hover:bg-amber-700 disabled:bg-amber-400 text-white text-sm rounded-lg transition-colors"
                    >
                      {isCodeSent ? `${countdown}s` : '获取验证码'}
                    </button>
                  </div>
                </div>

                {/* 按钮组 */}
                <div className="flex space-x-3 pt-4">
                  <Button
                    variant="outline"
                    onClick={handleClose}
                    disabled={isSubmitting}
                    className="flex-1 border-amber-600 text-amber-700 hover:bg-amber-600 hover:text-white"
                  >
                    取消
                  </Button>
                  <Button
                    variant="primary"
                    type="submit"
                    loading={isSubmitting}
                    disabled={!userInfo.name.trim() || !userInfo.phone.trim() || !userInfo.verificationCode.trim()}
                    className="flex-1 bg-amber-600 hover:bg-amber-700 text-white"
                  >
                    提交
                  </Button>
                </div>
              </form>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
