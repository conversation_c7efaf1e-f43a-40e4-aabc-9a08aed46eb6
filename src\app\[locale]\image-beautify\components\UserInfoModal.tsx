/**
 * 用户信息收集弹窗组件
 */

'use client'

import { useState, useCallback } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { useTranslation } from '@/lib/i18n/client'
import Image from 'next/image'
import retentionBg from '/public/images/retentionBg.png'


interface UserInfo {
  name: string
  phone: string
  verificationCode: string
}

interface UserInfoModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (userInfo: UserInfo) => void
  isSubmitting?: boolean
}

export default function UserInfoModal({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false
}: UserInfoModalProps) {
  const { t } = useTranslation()
  const [userInfo, setUserInfo] = useState<UserInfo>({
    name: '',
    phone: '',
    verificationCode: ''
  })
  const [isCodeSent, setIsCodeSent] = useState(false)
  const [countdown, setCountdown] = useState(0)

  // 发送验证码
  const handleSendCode = useCallback(async () => {
    if (!userInfo.phone.trim()) {
      return
    }
    
    // TODO: 实际发送验证码的逻辑
    setIsCodeSent(true)
    setCountdown(60)
    
    // 倒计时
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer)
          setIsCodeSent(false)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }, [userInfo.phone])

  // 处理表单提交
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    if (userInfo.name.trim() && userInfo.phone.trim() && userInfo.verificationCode.trim()) {
      onSubmit(userInfo)
    }
  }, [userInfo, onSubmit])

  // 处理输入变化
  const handleInputChange = useCallback((field: keyof UserInfo, value: string) => {
    setUserInfo(prev => ({ ...prev, [field]: value }))
  }, [])

  // 重置表单
  const handleClose = useCallback(() => {
    setUserInfo({ name: '', phone: '', verificationCode: '' })
    setIsCodeSent(false)
    setCountdown(0)
    onClose()
  }, [onClose])

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 "
          onClick={handleClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="relative lg:w-[860px] bg-white lg:h-[600px]"
            onClick={(e) => e.stopPropagation()}
          >
            {/* 背景图片 */}
            <div className="relative h-full">
              <Image
                src={retentionBg}
                alt="Background"
                fill
                className="object-cover"
                priority
              />

              {/* 内容覆盖层 */}
              <div className="relative z-10 flex h-full">
                {/* 左侧图片区域 - 在移动端隐藏 */}
                <div className="hidden md:block flex-1" />

                {/* 右侧表单区域 */}
                <div className="flex-1 lg:pr-[55px] flex flex-col  text-white">
                  {/* 标题区域 */}
                  <div className="my-[60px]">
                    <h2 className="text-[20px] lg:text-[30px] font-[300] text-center">
                      {t('retention.title', '免费AI变美')}
                    </h2>
                    <p className="text-[14px] lg:text-[16px] font-[300] text-center">
                      {t('retention.subTitle', '提交后即可下载')}
                    </p>
                  </div>

                  {/* 表单 */}
                  <form onSubmit={handleSubmit} className="space-y-[30px]">
                    {/* 姓名和城市输入 */}
                    <div className="flex space-x-5">
                      <div className="flex-1">
                        <label className="block text-[12px] font-[300] text-white leading-[16px] mb-4">
                          {t('retention.userName', '姓名')}
                        </label>
                        <input
                          type="text"
                          value={userInfo.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          placeholder={t('retention.userNamePlaceholder', '输入姓名')}
                          className="w-full px-0 py-1 bg-transparent border-0 border-b border-white/30 text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none focus:border-white transition-colors"
                          disabled={isSubmitting}
                          required
                        />
                      </div>
                      <div className="flex-1">
                        <label className="block text-[12px] font-[300] text-white leading-[16px] mb-4">
                          {t('retention.city', '城市')}
                        </label>
                        <input
                          type="text"
                          placeholder={t('retention.cityPlaceholder', '输入城市')}
                          className="w-full px-0 py-1 bg-transparent border-0 border-b border-white/30 text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none focus:border-white transition-colors"
                          disabled={isSubmitting}
                        />
                      </div>
                    </div>

                    {/* 手机号输入 */}
                    <div>
                      <label className="block text-[12px] font-[300] text-white leading-[16px] mb-4">
                        {t('retention.phone', '手机号码')}
                      </label>
                      <input
                        type="tel"
                        value={userInfo.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder={t('retention.phonePlaceholder', '输入手机号码')}
                        className="w-full px-0 py-1 bg-transparent border-0 border-b border-white/30 text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none focus:border-white transition-colors"
                        disabled={isSubmitting}
                        required
                      />
                    </div>

                    {/* 验证码输入 */}
                    <div>
                      <label className="block text-[12px] font-[300] text-white leading-[16px] mb-4">
                        {t('retention.verificationCode', '验证码')}
                      </label>
                      <div className="flex items-end space-x-4">
                        <div className="flex-1">
                          <input
                            type="text"
                            value={userInfo.verificationCode}
                            onChange={(e) => handleInputChange('verificationCode', e.target.value)}
                            placeholder={t('retention.verificationCodePlaceholder', '输入验证码')}
                            className="w-full px-0 py-2 bg-transparent border-0 border-b border-white/30 text-white placeholder:text-white placeholder:text-sm placeholder:font-light focus:outline-none focus:border-white transition-colors"
                            disabled={isSubmitting}
                            required
                          />
                        </div>
                        <button
                          type="button"
                          onClick={handleSendCode}
                          disabled={isSubmitting || !userInfo.phone.trim() || isCodeSent}
                          className="px-4 py-2 text-white/80 hover:text-white text-sm border border-white/30 hover:border-white rounded transition-colors disabled:opacity-50"
                        >
                          {isCodeSent ? `${countdown}s` : t('retention.sendCode', '获取验证码')}
                        </button>
                      </div>
                    </div>

                    {/* 按钮组 */}
                    <div className="flex space-x-4 pt-6">
                      <button
                        type="button"
                        onClick={handleClose}
                        disabled={isSubmitting}
                        className="flex-1 px-6 py-3 text-white border border-white/30 hover:border-white rounded-lg transition-colors disabled:opacity-50"
                      >
                        {t('retention.cancel', '取消')}
                      </button>
                      <button
                        type="submit"
                        disabled={!userInfo.name.trim() || !userInfo.phone.trim() || !userInfo.verificationCode.trim() || isSubmitting}
                        className="flex-1 px-6 py-3 bg-white text-gray-900 hover:bg-white/90 rounded-lg transition-colors disabled:opacity-50 font-medium"
                      >
                        {isSubmitting ? '提交中...' : t('retention.submit', '提交')}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
